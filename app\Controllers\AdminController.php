<?php

namespace App\Controllers;

class AdminController extends BaseController
{
    /**
     * Administrator Settings Main Dashboard (GET request)
     * Displays sub-module navigation buttons
     *
     * @return string
     */
    public function index()
    {
        // Check if user is administrator (in real implementation)
        // For demo purposes, we'll allow access

        $data = [
            'page_title' => 'Administrator Settings',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => '']
            ]
        ];

        return view('admin/admin_index', $data);
    }

    // ========================================
    // USERS MANAGEMENT METHODS
    // ========================================

    /**
     * Display users list (GET request)
     *
     * @return string
     */
    public function users()
    {
        $data = [
            'page_title' => 'Users Management',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Users Management', 'url' => '']
            ],
            'users' => $this->getDummyUsers()
        ];

        return view('admin/admin_users', $data);
    }

    /**
     * Show create user form (GET request)
     *
     * @return string
     */
    public function createUser()
    {
        $data = [
            'page_title' => 'Create User',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Users Management', 'url' => site_url('admin/users')],
                ['title' => 'Create User', 'url' => '']
            ]
        ];

        return view('admin/admin_users_create', $data);
    }

    /**
     * Store new user (POST request)
     *
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    public function storeUser()
    {
        $input = $this->request->getPost();

        // Validate input (basic validation for demo)
        if (empty($input['full_name']) || empty($input['email'])) {
            return redirect()->back()->with('error', 'Full name and email are required.');
        }

        // In real implementation, save to database
        // For demo, return success message

        return redirect()->to('admin/users')->with('success', 'User created successfully.');
    }

    /**
     * Show edit user form (GET request)
     *
     * @param int $id
     * @return string
     */
    public function editUser($id = null)
    {
        if (!$id) {
            return redirect()->to('admin/users')->with('error', 'User ID is required.');
        }

        $user = $this->getDummyUserById($id);
        if (!$user) {
            return redirect()->to('admin/users')->with('error', 'User not found.');
        }

        $data = [
            'page_title' => 'Edit User',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Users Management', 'url' => site_url('admin/users')],
                ['title' => 'Edit User', 'url' => '']
            ],
            'user' => $user
        ];

        return view('admin/admin_users_edit', $data);
    }

    /**
     * Update user (PUT request)
     *
     * @param int $id
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    public function updateUser($id = null)
    {
        if (!$id) {
            return redirect()->to('admin/users')->with('error', 'User ID is required.');
        }

        $input = $this->request->getPost();

        // In real implementation, update database
        // For demo, return success message

        return redirect()->to('admin/users')->with('success', 'User updated successfully.');
    }

    /**
     * Delete user (DELETE request)
     *
     * @param int $id
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    public function deleteUser($id = null)
    {
        if (!$id) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'User ID is required'
            ])->setStatusCode(400);
        }

        // In real implementation, delete from database
        // For demo, return success response

        return $this->response->setJSON([
            'status' => 'success',
            'message' => 'User deleted successfully'
        ]);
    }

    // ========================================
    // STRUCTURE MANAGEMENT METHODS
    // ========================================

    /**
     * Display structures list (GET request)
     *
     * @return string
     */
    public function structures()
    {
        $data = [
            'page_title' => 'Structure Management',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Structure Management', 'url' => '']
            ],
            'structures' => $this->getDummyStructures(),
            'groups' => $this->getDummyGroups(),
            'positions' => $this->getDummyPositions()
        ];

        return view('admin/admin_structures', $data);
    }

    /**
     * Show create structure form (GET request)
     *
     * @return string
     */
    public function createStructure()
    {
        $data = [
            'page_title' => 'Create Structure',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Structure Management', 'url' => site_url('admin/structures')],
                ['title' => 'Create Structure', 'url' => '']
            ]
        ];

        return view('admin/admin_structures_create', $data);
    }

    /**
     * Store new structure (POST request)
     *
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    public function storeStructure()
    {
        $input = $this->request->getPost();

        // In real implementation, save to database
        // For demo, return success message

        return redirect()->to('admin/structures')->with('success', 'Structure created successfully.');
    }

    // ========================================
    // APPOINTMENTS METHODS
    // ========================================

    /**
     * Display appointments list (GET request)
     *
     * @return string
     */
    public function appointments()
    {
        $data = [
            'page_title' => 'Appointments Management',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Appointments', 'url' => '']
            ],
            'appointments' => $this->getDummyAppointments(),
            'users' => $this->getDummyUsers(),
            'positions' => $this->getDummyPositions()
        ];

        return view('admin/admin_appointments', $data);
    }

    /**
     * Show create appointment form (GET request)
     *
     * @return string
     */
    public function createAppointment()
    {
        $data = [
            'page_title' => 'Create Appointment',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Appointments', 'url' => site_url('admin/appointments')],
                ['title' => 'Create Appointment', 'url' => '']
            ],
            'users' => $this->getDummyUsers(),
            'positions' => $this->getDummyPositions()
        ];

        return view('admin/admin_appointments_create', $data);
    }

    /**
     * Store new appointment (POST request)
     *
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    public function storeAppointment()
    {
        $input = $this->request->getPost();

        // In real implementation, save to database
        // For demo, return success message

        return redirect()->to('admin/appointments')->with('success', 'Appointment created successfully.');
    }

    // ========================================
    // PLANS MANAGEMENT METHODS
    // ========================================

    /**
     * Display plans management dashboard (GET request)
     *
     * @return string
     */
    public function plans()
    {
        $data = [
            'page_title' => 'Plans Management',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Plans Management', 'url' => '']
            ],
            'corporate_plans' => $this->getDummyCorporatePlans(),
            'development_plans' => $this->getDummyDevelopmentPlans()
        ];

        return view('admin/admin_plans', $data);
    }

    // ========================================
    // BUDGET BOOK MANAGEMENT METHODS
    // ========================================

    /**
     * Display budget books list (GET request)
     *
     * @return string
     */
    public function budgetBooks()
    {
        $data = [
            'page_title' => 'Budget Book Management',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => site_url('dashboard')],
                ['title' => 'Administrator Settings', 'url' => site_url('admin')],
                ['title' => 'Budget Book Management', 'url' => '']
            ],
            'budget_books' => $this->getDummyBudgetBooks(),
            'budget_codes' => $this->getDummyBudgetCodes()
        ];

        return view('admin/admin_budget_books', $data);
    }

    // ========================================
    // DUMMY DATA METHODS
    // ========================================

    /**
     * Get dummy users data
     *
     * @return array
     */
    private function getDummyUsers()
    {
        return [
            [
                'id' => 1,
                'full_name' => 'John Doe',
                'email' => '<EMAIL>',
                'file_number' => 'EMP001',
                'gender' => 'Male',
                'date_of_birth' => '1985-03-15',
                'date_joined' => '2020-01-15',
                'id_photo' => 'john_doe.jpg',
                'contact_details' => '+675 123 4567',
                'remarks' => 'Senior Administrator'
            ],
            [
                'id' => 2,
                'full_name' => 'Mary Smith',
                'email' => '<EMAIL>',
                'file_number' => 'EMP002',
                'gender' => 'Female',
                'date_of_birth' => '1990-07-22',
                'date_joined' => '2021-03-10',
                'id_photo' => 'mary_smith.jpg',
                'contact_details' => '+675 234 5678',
                'remarks' => 'Finance Officer'
            ],
            [
                'id' => 3,
                'full_name' => 'Peter Wilson',
                'email' => '<EMAIL>',
                'file_number' => 'EMP003',
                'gender' => 'Male',
                'date_of_birth' => '1988-11-08',
                'date_joined' => '2019-09-01',
                'id_photo' => 'peter_wilson.jpg',
                'contact_details' => '+675 345 6789',
                'remarks' => 'Project Manager'
            ],
            [
                'id' => 4,
                'full_name' => 'Sarah Johnson',
                'email' => '<EMAIL>',
                'file_number' => 'EMP004',
                'gender' => 'Female',
                'date_of_birth' => '1992-05-18',
                'date_joined' => '2022-01-20',
                'id_photo' => 'sarah_johnson.jpg',
                'contact_details' => '+675 456 7890',
                'remarks' => 'HR Specialist'
            ]
        ];
    }

    /**
     * Get dummy user by ID
     *
     * @param int $id
     * @return array|null
     */
    private function getDummyUserById($id)
    {
        $users = $this->getDummyUsers();
        foreach ($users as $user) {
            if ($user['id'] == $id) {
                return $user;
            }
        }
        return null;
    }

    /**
     * Get dummy structures data
     *
     * @return array
     */
    private function getDummyStructures()
    {
        return [
            [
                'id' => 1,
                'name' => 'IPMS Organizational Structure 2024',
                'is_active' => true,
                'created_at' => '2024-01-01 00:00:00'
            ],
            [
                'id' => 2,
                'name' => 'IPMS Organizational Structure 2023',
                'is_active' => false,
                'created_at' => '2023-01-01 00:00:00'
            ]
        ];
    }

    /**
     * Get dummy groups data
     *
     * @return array
     */
    private function getDummyGroups()
    {
        return [
            [
                'id' => 1,
                'structure_id' => 1,
                'parent_group_id' => null,
                'name' => 'Executive Management',
                'level' => 0
            ],
            [
                'id' => 2,
                'structure_id' => 1,
                'parent_group_id' => 1,
                'name' => 'Finance Department',
                'level' => 1
            ],
            [
                'id' => 3,
                'structure_id' => 1,
                'parent_group_id' => 1,
                'name' => 'Human Resources Department',
                'level' => 1
            ],
            [
                'id' => 4,
                'structure_id' => 1,
                'parent_group_id' => 1,
                'name' => 'IT Department',
                'level' => 1
            ],
            [
                'id' => 5,
                'structure_id' => 1,
                'parent_group_id' => 2,
                'name' => 'Budget Planning Unit',
                'level' => 2
            ]
        ];
    }

    /**
     * Get dummy positions data
     *
     * @return array
     */
    private function getDummyPositions()
    {
        return [
            [
                'id' => 1,
                'group_id' => 1,
                'position_no' => 'POS001',
                'position_name' => 'Chief Executive Officer',
                'grade' => 'CEO',
                'reporting_to_id' => null,
                'position_type' => 'Public Servant',
                'is_fund_manager' => false,
                'is_supervisor' => true,
                'is_group_admin' => true
            ],
            [
                'id' => 2,
                'group_id' => 2,
                'position_no' => 'POS002',
                'position_name' => 'Finance Manager',
                'grade' => 'Grade 12',
                'reporting_to_id' => 1,
                'position_type' => 'Public Servant',
                'is_fund_manager' => true,
                'is_supervisor' => true,
                'is_group_admin' => true
            ],
            [
                'id' => 3,
                'group_id' => 3,
                'position_no' => 'POS003',
                'position_name' => 'HR Manager',
                'grade' => 'Grade 11',
                'reporting_to_id' => 1,
                'position_type' => 'Public Servant',
                'is_fund_manager' => false,
                'is_supervisor' => true,
                'is_group_admin' => true
            ],
            [
                'id' => 4,
                'group_id' => 5,
                'position_no' => 'POS004',
                'position_name' => 'Budget Analyst',
                'grade' => 'Grade 9',
                'reporting_to_id' => 2,
                'position_type' => 'Public Servant',
                'is_fund_manager' => false,
                'is_supervisor' => false,
                'is_group_admin' => false
            ]
        ];
    }

    /**
     * Get dummy appointments data
     *
     * @return array
     */
    private function getDummyAppointments()
    {
        return [
            [
                'id' => 1,
                'user_id' => 1,
                'position_id' => 1,
                'user_name' => 'John Doe',
                'position_name' => 'Chief Executive Officer',
                'group_name' => 'Executive Management',
                'start_date' => '2020-01-15',
                'end_date' => null,
                'status' => 'Active'
            ],
            [
                'id' => 2,
                'user_id' => 2,
                'position_id' => 2,
                'user_name' => 'Mary Smith',
                'position_name' => 'Finance Manager',
                'group_name' => 'Finance Department',
                'start_date' => '2021-03-10',
                'end_date' => null,
                'status' => 'Active'
            ],
            [
                'id' => 3,
                'user_id' => 3,
                'position_id' => 3,
                'user_name' => 'Peter Wilson',
                'position_name' => 'HR Manager',
                'group_name' => 'Human Resources Department',
                'start_date' => '2019-09-01',
                'end_date' => null,
                'status' => 'Active'
            ]
        ];
    }

    /**
     * Get dummy corporate plans data
     *
     * @return array
     */
    private function getDummyCorporatePlans()
    {
        return [
            [
                'id' => 1,
                'name' => 'IPMS Corporate Plan 2024-2026',
                'type' => 'Corporate',
                'fiscal_year' => 2024,
                'kras_count' => 5,
                'kpis_count' => 15
            ],
            [
                'id' => 2,
                'name' => 'IPMS Corporate Plan 2021-2023',
                'type' => 'Corporate',
                'fiscal_year' => 2021,
                'kras_count' => 4,
                'kpis_count' => 12
            ]
        ];
    }

    /**
     * Get dummy development plans data
     *
     * @return array
     */
    private function getDummyDevelopmentPlans()
    {
        return [
            [
                'id' => 3,
                'name' => 'IPMS Development Plan 2024-2026',
                'type' => 'Development',
                'fiscal_year' => 2024,
                'programs_count' => 3,
                'projects_count' => 8
            ],
            [
                'id' => 4,
                'name' => 'IPMS Development Plan 2021-2023',
                'type' => 'Development',
                'fiscal_year' => 2021,
                'programs_count' => 2,
                'projects_count' => 6
            ]
        ];
    }

    /**
     * Get dummy budget books data
     *
     * @return array
     */
    private function getDummyBudgetBooks()
    {
        return [
            [
                'id' => 1,
                'fiscal_year' => 2024,
                'name' => 'IPMS Budget Book 2024',
                'total_revenue' => 'K 2,500,000',
                'total_expenditure' => 'K 2,300,000',
                'codes_count' => 45
            ],
            [
                'id' => 2,
                'fiscal_year' => 2023,
                'name' => 'IPMS Budget Book 2023',
                'total_revenue' => 'K 2,200,000',
                'total_expenditure' => 'K 2,100,000',
                'codes_count' => 42
            ]
        ];
    }

    /**
     * Get dummy budget codes data
     *
     * @return array
     */
    private function getDummyBudgetCodes()
    {
        return [
            [
                'id' => 1,
                'budget_book_id' => 1,
                'code' => 'REV001',
                'item' => 'Government Grants',
                'amount' => 'K 1,500,000',
                'type' => 'Revenue',
                'linked_groups' => 'Executive Management',
                'remarks' => 'Annual government funding'
            ],
            [
                'id' => 2,
                'budget_book_id' => 1,
                'code' => 'EXP001',
                'item' => 'Staff Salaries',
                'amount' => 'K 800,000',
                'type' => 'Expenditure',
                'linked_groups' => 'All Departments',
                'remarks' => 'Annual staff compensation'
            ],
            [
                'id' => 3,
                'budget_book_id' => 1,
                'code' => 'EXP002',
                'item' => 'IT Infrastructure',
                'amount' => 'K 250,000',
                'type' => 'Expenditure',
                'linked_groups' => 'IT Department',
                'remarks' => 'Hardware and software upgrades'
            ],
            [
                'id' => 4,
                'budget_book_id' => 1,
                'code' => 'EXP003',
                'item' => 'Training and Development',
                'amount' => 'K 150,000',
                'type' => 'Expenditure',
                'linked_groups' => 'Human Resources Department',
                'remarks' => 'Staff capacity building programs'
            ]
        ];
    }
}