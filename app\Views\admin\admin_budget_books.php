<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .admin-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.12);
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .budget-book-card {
        border-left: 4px solid #28a745;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .budget-book-card:hover {
        border-left-color: #800000;
        transform: translateY(-3px);
    }

    .budget-book-card.current {
        border-left-color: #007bff;
        background: linear-gradient(135deg, #f8f9fa, #e3f2fd);
    }

    .table th {
        background-color: #f8f9fa;
        border-top: none;
        color: #800000;
        font-weight: 600;
    }

    .btn-action {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        margin: 0 0.125rem;
    }

    .nav-tabs .nav-link {
        color: #800000;
        border: none;
        border-bottom: 2px solid transparent;
    }

    .nav-tabs .nav-link.active {
        color: #28a745;
        border-bottom-color: #28a745;
        background: none;
    }

    .amount-positive {
        color: #28a745;
        font-weight: 600;
    }

    .amount-negative {
        color: #dc3545;
        font-weight: 600;
    }

    .code-badge {
        font-family: 'Courier New', monospace;
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }

    .metric-card {
        text-align: center;
        padding: 1.5rem;
        border-radius: 10px;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    }

    .metric-value {
        font-size: 1.5rem;
        font-weight: bold;
        color: #800000;
    }

    .metric-label {
        color: #6c757d;
        font-size: 0.875rem;
        margin-top: 0.5rem;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-book me-2"></i>Budget Book Management
                        </h3>
                        <p class="text-muted mb-0">
                            Manage budget books, revenue and expenditure codes. Link budget codes to organizational groups for workplan activities.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="btn-group">
                            <a href="<?= site_url('admin') ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Admin
                            </a>
                            <button class="btn btn-success dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-plus me-2"></i>Create New
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#"><i class="fas fa-book me-2"></i>Budget Book</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-code me-2"></i>Budget Code</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Budget Overview -->
    <div class="row g-4 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="metric-card">
                <div class="metric-value"><?= count($budget_books) ?></div>
                <div class="metric-label">Budget Books</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="metric-card">
                <div class="metric-value"><?= count($budget_codes) ?></div>
                <div class="metric-label">Budget Codes</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="metric-card">
                <div class="metric-value amount-positive">K 4.7M</div>
                <div class="metric-label">Total Revenue</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="metric-card">
                <div class="metric-value amount-negative">K 4.4M</div>
                <div class="metric-label">Total Expenditure</div>
            </div>
        </div>
    </div>

    <!-- Budget Books -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="fw-bold mb-0" style="color: #800000;">
                        <i class="fas fa-book me-2"></i>Budget Books
                    </h5>
                    <button class="btn btn-success btn-sm">
                        <i class="fas fa-plus me-1"></i>Add Budget Book
                    </button>
                </div>

                <div class="row g-4">
                    <?php foreach ($budget_books as $index => $book): ?>
                    <div class="col-lg-6">
                        <div class="budget-book-card <?= $index === 0 ? 'current' : '' ?> admin-card p-4">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div>
                                    <h6 class="fw-bold mb-1"><?= esc($book['name']) ?></h6>
                                    <span class="badge bg-primary">FY <?= $book['fiscal_year'] ?></span>
                                    <?php if ($index === 0): ?>
                                        <span class="badge bg-success ms-1">Current</span>
                                    <?php endif; ?>
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary btn-sm" type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#"><i class="fas fa-eye me-2"></i>View Details</a></li>
                                        <li><a class="dropdown-item" href="#"><i class="fas fa-edit me-2"></i>Edit Book</a></li>
                                        <li><a class="dropdown-item" href="#"><i class="fas fa-plus me-2"></i>Add Code</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#"><i class="fas fa-trash me-2"></i>Delete</a></li>
                                    </ul>
                                </div>
                            </div>

                            <div class="row text-center mb-3">
                                <div class="col-4">
                                    <div class="fw-bold amount-positive"><?= $book['total_revenue'] ?></div>
                                    <small class="text-muted">Revenue</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold amount-negative"><?= $book['total_expenditure'] ?></div>
                                    <small class="text-muted">Expenditure</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold text-info"><?= $book['codes_count'] ?></div>
                                    <small class="text-muted">Codes</small>
                                </div>
                            </div>

                            <div class="btn-group w-100" role="group">
                                <button class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-list me-1"></i>View Codes
                                </button>
                                <button class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-chart-pie me-1"></i>Reports
                                </button>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Budget Codes -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card p-4">
                <!-- Tab Navigation -->
                <ul class="nav nav-tabs mb-4" id="budgetTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="all-codes-tab" data-bs-toggle="tab"
                                data-bs-target="#all-codes" type="button" role="tab">
                            <i class="fas fa-list me-2"></i>All Codes
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="revenue-tab" data-bs-toggle="tab"
                                data-bs-target="#revenue" type="button" role="tab">
                            <i class="fas fa-arrow-up me-2"></i>Revenue
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="expenditure-tab" data-bs-toggle="tab"
                                data-bs-target="#expenditure" type="button" role="tab">
                            <i class="fas fa-arrow-down me-2"></i>Expenditure
                        </button>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content" id="budgetTabContent">
                    <!-- All Codes Tab -->
                    <div class="tab-pane fade show active" id="all-codes" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="fw-bold mb-0" style="color: #800000;">
                                <i class="fas fa-code me-2"></i>Budget Codes
                            </h5>
                            <div class="d-flex gap-2">
                                <select class="form-select form-select-sm" style="width: auto;">
                                    <option>All Types</option>
                                    <option>Revenue</option>
                                    <option>Expenditure</option>
                                </select>
                                <input type="text" class="form-control form-control-sm" placeholder="Search codes..." style="width: 200px;">
                                <button class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Code</th>
                                        <th>Item</th>
                                        <th>Type</th>
                                        <th>Amount</th>
                                        <th>Linked Groups</th>
                                        <th>Remarks</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($budget_codes as $code): ?>
                                    <tr>
                                        <td>
                                            <span class="badge bg-dark code-badge"><?= esc($code['code']) ?></span>
                                        </td>
                                        <td>
                                            <strong><?= esc($code['item']) ?></strong>
                                        </td>
                                        <td>
                                            <?php if ($code['type'] === 'Revenue'): ?>
                                                <span class="badge bg-success"><?= esc($code['type']) ?></span>
                                            <?php else: ?>
                                                <span class="badge bg-danger"><?= esc($code['type']) ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="<?= $code['type'] === 'Revenue' ? 'amount-positive' : 'amount-negative' ?>">
                                                <?= esc($code['amount']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?= esc($code['linked_groups']) ?></small>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?= esc($code['remarks']) ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-outline-primary btn-action" title="Edit Code">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-info btn-action" title="Link Groups">
                                                    <i class="fas fa-link"></i>
                                                </button>
                                                <button class="btn btn-outline-danger btn-action" title="Delete Code">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Revenue Tab -->
                    <div class="tab-pane fade" id="revenue" role="tabpanel">
                        <div class="alert alert-success">
                            <i class="fas fa-info-circle me-2"></i>
                            Revenue codes represent income sources for the organization.
                        </div>
                        <!-- Revenue codes would be filtered here -->
                    </div>

                    <!-- Expenditure Tab -->
                    <div class="tab-pane fade" id="expenditure" role="tabpanel">
                        <div class="alert alert-danger">
                            <i class="fas fa-info-circle me-2"></i>
                            Expenditure codes represent spending categories for workplan activities.
                        </div>
                        <!-- Expenditure codes would be filtered here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="d-flex gap-2">
                <a href="<?= site_url('admin') ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Admin Settings
                </a>
                <button class="btn btn-success">
                    <i class="fas fa-plus me-2"></i>Create Budget Book
                </button>
                <button class="btn btn-outline-primary">
                    <i class="fas fa-download me-2"></i>Export Budget Data
                </button>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Budget book card click handlers
        document.querySelectorAll('.budget-book-card').forEach(card => {
            card.addEventListener('click', function(e) {
                // Don't trigger if clicking on dropdown or buttons
                if (e.target.closest('.dropdown') || e.target.closest('.btn-group')) {
                    return;
                }

                // Add visual feedback
                this.style.transform = 'translateY(-5px)';
                setTimeout(() => {
                    this.style.transform = 'translateY(-3px)';
                }, 150);

                console.log('Budget book card clicked');
            });
        });

        // Search functionality
        document.querySelector('input[placeholder="Search codes..."]').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const tableRows = document.querySelectorAll('tbody tr');

            tableRows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        });

        console.log('Budget Book Management initialized successfully');
    });
</script>
<?= $this->endSection() ?>
