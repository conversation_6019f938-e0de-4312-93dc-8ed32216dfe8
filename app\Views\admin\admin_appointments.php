<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .admin-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.12);
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .table th {
        background-color: #f8f9fa;
        border-top: none;
        color: #800000;
        font-weight: 600;
    }

    .btn-action {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        margin: 0 0.125rem;
    }

    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .appointment-card {
        border-left: 4px solid #28a745;
        transition: all 0.3s ease;
    }

    .appointment-card:hover {
        border-left-color: #800000;
        transform: translateX(5px);
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-user-tie me-2"></i>Appointments Management
                        </h3>
                        <p class="text-muted mb-0">
                            Assign employees to positions within the organizational structure. Manage current and historical appointments.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="btn-group">
                            <a href="<?= site_url('admin/appointments/create') ?>" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>Create Appointment
                            </a>
                            <button class="btn btn-outline-info dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-download me-2"></i>Import
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#"><i class="fas fa-file-csv me-2"></i>Import from CSV</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-file-excel me-2"></i>Import from Excel</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Statistics -->
    <div class="row g-4 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="admin-card p-3 text-center">
                <i class="fas fa-user-check fs-1 mb-2 text-success"></i>
                <h5 class="fw-bold"><?= count($appointments) ?></h5>
                <small class="text-muted">Active Appointments</small>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="admin-card p-3 text-center">
                <i class="fas fa-users fs-1 mb-2 text-primary"></i>
                <h5 class="fw-bold"><?= count($users) ?></h5>
                <small class="text-muted">Total Users</small>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="admin-card p-3 text-center">
                <i class="fas fa-briefcase fs-1 mb-2 text-info"></i>
                <h5 class="fw-bold"><?= count($positions) ?></h5>
                <small class="text-muted">Available Positions</small>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="admin-card p-3 text-center">
                <i class="fas fa-clock fs-1 mb-2 text-warning"></i>
                <h5 class="fw-bold">0</h5>
                <small class="text-muted">Pending Appointments</small>
            </div>
        </div>
    </div>

    <!-- Appointments Table -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="fw-bold mb-0" style="color: #800000;">
                        <i class="fas fa-list me-2"></i>Current Appointments
                    </h5>
                    <div class="d-flex gap-2">
                        <select class="form-select form-select-sm" style="width: auto;">
                            <option>All Statuses</option>
                            <option>Active</option>
                            <option>Inactive</option>
                            <option>Pending</option>
                        </select>
                        <input type="text" class="form-control form-control-sm" placeholder="Search appointments..." style="width: 200px;">
                        <button class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Employee</th>
                                <th>Position</th>
                                <th>Group</th>
                                <th>Start Date</th>
                                <th>End Date</th>
                                <th>Status</th>
                                <th>Duration</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($appointments as $appointment): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" 
                                                 style="width: 40px; height: 40px;">
                                                <span class="text-white fw-bold">
                                                    <?= strtoupper(substr($appointment['user_name'], 0, 2)) ?>
                                                </span>
                                            </div>
                                        </div>
                                        <div>
                                            <strong><?= esc($appointment['user_name']) ?></strong>
                                            <br><small class="text-muted">ID: <?= $appointment['user_id'] ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <strong><?= esc($appointment['position_name']) ?></strong>
                                    <br><small class="text-muted">Pos ID: <?= $appointment['position_id'] ?></small>
                                </td>
                                <td><?= esc($appointment['group_name']) ?></td>
                                <td><?= date('M d, Y', strtotime($appointment['start_date'])) ?></td>
                                <td>
                                    <?php if ($appointment['end_date']): ?>
                                        <?= date('M d, Y', strtotime($appointment['end_date'])) ?>
                                    <?php else: ?>
                                        <span class="text-muted">Ongoing</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-success status-badge"><?= esc($appointment['status']) ?></span>
                                </td>
                                <td>
                                    <?php 
                                    $start = new DateTime($appointment['start_date']);
                                    $end = $appointment['end_date'] ? new DateTime($appointment['end_date']) : new DateTime();
                                    $duration = $start->diff($end);
                                    ?>
                                    <small class="text-muted">
                                        <?= $duration->y ?> years, <?= $duration->m ?> months
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-outline-primary btn-action" title="Edit Appointment">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-warning btn-action" title="End Appointment">
                                            <i class="fas fa-stop"></i>
                                        </button>
                                        <button class="btn btn-outline-danger btn-action" title="Delete Appointment">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="text-muted">
                        Showing <?= count($appointments) ?> of <?= count($appointments) ?> appointments
                    </div>
                    <nav aria-label="Appointments pagination">
                        <ul class="pagination pagination-sm mb-0">
                            <li class="page-item disabled">
                                <span class="page-link">Previous</span>
                            </li>
                            <li class="page-item active">
                                <span class="page-link">1</span>
                            </li>
                            <li class="page-item disabled">
                                <span class="page-link">Next</span>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <h5 class="fw-bold mb-3" style="color: #800000;">
                    <i class="fas fa-history me-2"></i>Recent Appointment Activity
                </h5>
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="appointment-card admin-card p-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-user-plus fs-4 text-success me-3"></i>
                                <div>
                                    <strong>New Appointment</strong>
                                    <br><small class="text-muted">Sarah Johnson appointed as HR Specialist - 2 hours ago</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="appointment-card admin-card p-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-user-edit fs-4 text-info me-3"></i>
                                <div>
                                    <strong>Position Change</strong>
                                    <br><small class="text-muted">Peter Wilson promoted to HR Manager - 1 day ago</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="d-flex gap-2">
                <a href="<?= site_url('admin') ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Admin Settings
                </a>
                <a href="<?= site_url('admin/appointments/create') ?>" class="btn btn-success">
                    <i class="fas fa-plus me-2"></i>Create New Appointment
                </a>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Search functionality
        document.querySelector('input[placeholder="Search appointments..."]').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const tableRows = document.querySelectorAll('tbody tr');
            
            tableRows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        });

        // Status filter
        document.querySelector('select').addEventListener('change', function() {
            const filterValue = this.value;
            const tableRows = document.querySelectorAll('tbody tr');
            
            tableRows.forEach(row => {
                if (filterValue === 'All Statuses') {
                    row.style.display = '';
                } else {
                    const statusCell = row.querySelector('.status-badge');
                    const status = statusCell ? statusCell.textContent.trim() : '';
                    row.style.display = status === filterValue ? '' : 'none';
                }
            });
        });

        console.log('Appointments Management initialized successfully');
    });
</script>
<?= $this->endSection() ?>
