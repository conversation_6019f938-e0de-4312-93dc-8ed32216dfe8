<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?php
/**
 * Administrator Settings Main Dashboard
 *
 * This view displays the main administrator settings interface with sub-module navigation buttons.
 * Features mobile app-like button interface as specified in the UI design writeup.
 *
 * Sub-modules:
 * - Users Management
 * - Structure Management
 * - Appointments
 * - Plans Management
 * - Budget Book Management
 */
?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .admin-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .admin-module-btn {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        text-decoration: none;
        color: #343a40;
        display: block;
        height: 100%;
        min-height: 180px;
    }

    .admin-module-btn:hover {
        border-color: #800000;
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        color: #800000;
        text-decoration: none;
    }

    .admin-module-btn i {
        font-size: 3rem;
        color: #28a745;
        margin-bottom: 1rem;
        display: block;
    }

    .admin-module-btn:hover i {
        color: #800000;
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .breadcrumb-item.active {
        color: #6c757d;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-tools me-2"></i>Administrator Settings
                        </h3>
                        <p class="text-muted mb-0">
                            Manage users, organizational structure, appointments, plans, and budget books.
                            Select a module below to get started.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="d-flex align-items-center justify-content-md-end gap-3">
                            <a href="<?= site_url('dashboard') ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                            </a>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-user-shield me-2 text-success fs-4"></i>
                                <span class="text-muted">Administrator Access</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Administrator Sub-Modules -->
    <div class="row g-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <h4 class="fw-bold mb-4" style="color: #800000;">
                    <i class="fas fa-th-large me-2"></i>Administration Modules
                </h4>
                <div class="row g-4">
                    <!-- Users Management -->
                    <div class="col-lg-4 col-md-6">
                        <a href="<?= site_url('admin/users') ?>" class="admin-module-btn">
                            <i class="fas fa-users"></i>
                            <h6 class="fw-bold">Users Management</h6>
                            <small class="text-muted">Create, edit, and manage system users</small>
                        </a>
                    </div>

                    <!-- Structure Management -->
                    <div class="col-lg-4 col-md-6">
                        <a href="<?= site_url('admin/structures') ?>" class="admin-module-btn">
                            <i class="fas fa-sitemap"></i>
                            <h6 class="fw-bold">Structure Management</h6>
                            <small class="text-muted">Manage organizational structures, groups, and positions</small>
                        </a>
                    </div>

                    <!-- Appointments -->
                    <div class="col-lg-4 col-md-6">
                        <a href="<?= site_url('admin/appointments') ?>" class="admin-module-btn">
                            <i class="fas fa-user-tie"></i>
                            <h6 class="fw-bold">Appointments</h6>
                            <small class="text-muted">Assign employees to positions</small>
                        </a>
                    </div>

                    <!-- Plans Management -->
                    <div class="col-lg-4 col-md-6">
                        <a href="<?= site_url('admin/plans') ?>" class="admin-module-btn">
                            <i class="fas fa-clipboard-list"></i>
                            <h6 class="fw-bold">Plans Management</h6>
                            <small class="text-muted">Manage corporate and development plans</small>
                        </a>
                    </div>

                    <!-- Budget Book Management -->
                    <div class="col-lg-4 col-md-6">
                        <a href="<?= site_url('admin/budget-books') ?>" class="admin-module-btn">
                            <i class="fas fa-book"></i>
                            <h6 class="fw-bold">Budget Book Management</h6>
                            <small class="text-muted">Manage budget books and revenue/expenditure codes</small>
                        </a>
                    </div>

                    <!-- Back to Dashboard -->
                    <div class="col-lg-4 col-md-6">
                        <a href="<?= site_url('dashboard') ?>" class="admin-module-btn" style="border-color: #28a745;">
                            <i class="fas fa-arrow-left" style="color: #28a745;"></i>
                            <h6 class="fw-bold">Back to Dashboard</h6>
                            <small class="text-muted">Return to main dashboard</small>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Statistics -->
    <div class="row g-4 mt-2">
        <div class="col-lg-3 col-md-6">
            <div class="admin-card p-3 text-center">
                <i class="fas fa-users fs-1 mb-2 text-primary"></i>
                <h5 class="fw-bold">4</h5>
                <small class="text-muted">Total Users</small>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="admin-card p-3 text-center">
                <i class="fas fa-sitemap fs-1 mb-2 text-success"></i>
                <h5 class="fw-bold">5</h5>
                <small class="text-muted">Active Groups</small>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="admin-card p-3 text-center">
                <i class="fas fa-user-tie fs-1 mb-2 text-info"></i>
                <h5 class="fw-bold">3</h5>
                <small class="text-muted">Active Appointments</small>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="admin-card p-3 text-center">
                <i class="fas fa-book fs-1 mb-2 text-warning"></i>
                <h5 class="fw-bold">2</h5>
                <small class="text-muted">Budget Books</small>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add hover effects to admin cards
        const adminCards = document.querySelectorAll('.admin-card');
        adminCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });

        // Add click handlers for module buttons
        const moduleButtons = document.querySelectorAll('.admin-module-btn');
        moduleButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Add loading effect
                const icon = this.querySelector('i');
                const originalClass = icon.className;
                icon.className = 'fas fa-spinner fa-spin';

                // Restore icon after a short delay (for visual feedback)
                setTimeout(() => {
                    icon.className = originalClass;
                }, 500);
            });
        });

        console.log('Administrator Settings initialized successfully');
    });
</script>
<?= $this->endSection() ?>
