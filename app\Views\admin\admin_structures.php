<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .admin-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.12);
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .table th {
        background-color: #f8f9fa;
        border-top: none;
        color: #800000;
        font-weight: 600;
    }

    .btn-action {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        margin: 0 0.125rem;
    }

    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .nav-tabs .nav-link {
        color: #800000;
        border: none;
        border-bottom: 2px solid transparent;
    }

    .nav-tabs .nav-link.active {
        color: #28a745;
        border-bottom-color: #28a745;
        background: none;
    }

    .group-level-0 { padding-left: 0; }
    .group-level-1 { padding-left: 1.5rem; }
    .group-level-2 { padding-left: 3rem; }
    .group-level-3 { padding-left: 4.5rem; }

    .position-type-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-sitemap me-2"></i>Structure Management
                        </h3>
                        <p class="text-muted mb-0">
                            Manage organizational structures, groups, and positions. Create hierarchical organizational charts.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= site_url('admin/structures/create') ?>" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>Create Structure
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Structure Management Tabs -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card p-4">
                <!-- Tab Navigation -->
                <ul class="nav nav-tabs mb-4" id="structureTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="structures-tab" data-bs-toggle="tab" 
                                data-bs-target="#structures" type="button" role="tab">
                            <i class="fas fa-building me-2"></i>Structures
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="groups-tab" data-bs-toggle="tab" 
                                data-bs-target="#groups" type="button" role="tab">
                            <i class="fas fa-users me-2"></i>Groups
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="positions-tab" data-bs-toggle="tab" 
                                data-bs-target="#positions" type="button" role="tab">
                            <i class="fas fa-user-tie me-2"></i>Positions
                        </button>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content" id="structureTabContent">
                    <!-- Structures Tab -->
                    <div class="tab-pane fade show active" id="structures" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="fw-bold mb-0" style="color: #800000;">
                                <i class="fas fa-building me-2"></i>Organizational Structures
                            </h5>
                            <button class="btn btn-success btn-sm">
                                <i class="fas fa-plus me-1"></i>Add Structure
                            </button>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Structure Name</th>
                                        <th>Status</th>
                                        <th>Created Date</th>
                                        <th>Groups Count</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($structures as $structure): ?>
                                    <tr>
                                        <td>
                                            <strong><?= esc($structure['name']) ?></strong>
                                        </td>
                                        <td>
                                            <?php if ($structure['is_active']): ?>
                                                <span class="badge bg-success status-badge">Active</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary status-badge">Inactive</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= date('M d, Y', strtotime($structure['created_at'])) ?></td>
                                        <td>
                                            <span class="badge bg-info">
                                                <?= count(array_filter($groups, function($g) use ($structure) { 
                                                    return $g['structure_id'] == $structure['id']; 
                                                })) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <?php if (!$structure['is_active']): ?>
                                                    <button class="btn btn-outline-success btn-action" title="Activate">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                <?php endif; ?>
                                                <button class="btn btn-outline-primary btn-action" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-danger btn-action" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Groups Tab -->
                    <div class="tab-pane fade" id="groups" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="fw-bold mb-0" style="color: #800000;">
                                <i class="fas fa-users me-2"></i>Organizational Groups
                            </h5>
                            <button class="btn btn-success btn-sm">
                                <i class="fas fa-plus me-1"></i>Add Group
                            </button>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Group Name</th>
                                        <th>Parent Group</th>
                                        <th>Level</th>
                                        <th>Positions Count</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($groups as $group): ?>
                                    <tr>
                                        <td class="group-level-<?= $group['level'] ?>">
                                            <?php for ($i = 0; $i < $group['level']; $i++): ?>
                                                <i class="fas fa-level-up-alt text-muted me-1" style="transform: rotate(90deg);"></i>
                                            <?php endfor; ?>
                                            <strong><?= esc($group['name']) ?></strong>
                                        </td>
                                        <td>
                                            <?php if ($group['parent_group_id']): ?>
                                                <?php 
                                                $parent = array_filter($groups, function($g) use ($group) { 
                                                    return $g['id'] == $group['parent_group_id']; 
                                                });
                                                $parent = reset($parent);
                                                ?>
                                                <?= $parent ? esc($parent['name']) : '-' ?>
                                            <?php else: ?>
                                                <span class="text-muted">Root Level</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">Level <?= $group['level'] ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">
                                                <?= count(array_filter($positions, function($p) use ($group) { 
                                                    return $p['group_id'] == $group['id']; 
                                                })) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-outline-primary btn-action" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-danger btn-action" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Positions Tab -->
                    <div class="tab-pane fade" id="positions" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="fw-bold mb-0" style="color: #800000;">
                                <i class="fas fa-user-tie me-2"></i>Positions
                            </h5>
                            <button class="btn btn-success btn-sm">
                                <i class="fas fa-plus me-1"></i>Add Position
                            </button>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Position No.</th>
                                        <th>Position Name</th>
                                        <th>Group</th>
                                        <th>Grade</th>
                                        <th>Type</th>
                                        <th>Roles</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($positions as $position): ?>
                                    <tr>
                                        <td>
                                            <span class="badge bg-primary"><?= esc($position['position_no']) ?></span>
                                        </td>
                                        <td>
                                            <strong><?= esc($position['position_name']) ?></strong>
                                        </td>
                                        <td>
                                            <?php 
                                            $group = array_filter($groups, function($g) use ($position) { 
                                                return $g['id'] == $position['group_id']; 
                                            });
                                            $group = reset($group);
                                            ?>
                                            <?= $group ? esc($group['name']) : '-' ?>
                                        </td>
                                        <td><?= esc($position['grade']) ?></td>
                                        <td>
                                            <span class="badge bg-info position-type-badge">
                                                <?= esc($position['position_type']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($position['is_fund_manager']): ?>
                                                <span class="badge bg-success position-type-badge me-1">Fund Manager</span>
                                            <?php endif; ?>
                                            <?php if ($position['is_supervisor']): ?>
                                                <span class="badge bg-warning position-type-badge me-1">Supervisor</span>
                                            <?php endif; ?>
                                            <?php if ($position['is_group_admin']): ?>
                                                <span class="badge bg-danger position-type-badge me-1">Group Admin</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-outline-primary btn-action" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-danger btn-action" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="d-flex gap-2">
                <a href="<?= site_url('admin') ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Admin Settings
                </a>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Tab switching functionality is handled by Bootstrap

        console.log('Structure Management initialized successfully');
    });
</script>
<?= $this->endSection() ?>
