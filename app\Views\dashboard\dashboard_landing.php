<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?>Dashboard<?= $this->endSection() ?>

<?= $this->section('page_title') ?>IPMS Dashboard<?= $this->endSection() ?>

<?php
/**
 * IPMS Dashboard - Role-Based Navigation Interface
 *
 * This dashboard implements the mobile app-like button interface as specified in the UI design writeup.
 * Features:
 * - Papua New Guinea Kina (K) currency symbols throughout
 * - Role-based button display (Standard, Group Admin, Supervisor, ARO, Fund Manager, Administrator)
 * - Mobile-friendly responsive design
 * - Dynamic button visibility based on user roles
 *
 * Standard Buttons (all users): My Workplan Activities, My Profile, My Acquittals, My Reports
 * Role-specific additions:
 * - Group Admin: Group Settings
 * - Supervisor: Manage Workplans
 * - ARO: Financial Claims
 * - Fund Manager: Financial Claims + Claims Workflow
 * - Administrator: Administrator Settings
 */
?>

<?= $this->section('styles') ?>
<style>
    .dashboard-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .stat-card {
        background: linear-gradient(135deg, #800000, #a00000);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
    }

    .stat-card.green {
        background: linear-gradient(135deg, #28a745, #20c997);
    }

    .stat-card.blue {
        background: linear-gradient(135deg, #007bff, #0056b3);
    }

    .stat-card.orange {
        background: linear-gradient(135deg, #fd7e14, #e55a00);
    }

    .module-btn {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        text-decoration: none;
        color: #343a40;
        display: block;
        height: 100%;
    }

    .module-btn:hover {
        border-color: #800000;
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        color: #800000;
        text-decoration: none;
    }

    .module-btn i {
        font-size: 3rem;
        color: #28a745;
        margin-bottom: 1rem;
        display: block;
    }

    .module-btn:hover i {
        color: #800000;
    }

    .recent-activity {
        max-height: 400px;
        overflow-y: auto;
    }

    .activity-item {
        padding: 1rem;
        border-bottom: 1px solid #e9ecef;
        transition: background-color 0.3s ease;
    }

    .activity-item:hover {
        background-color: #f8f9fa;
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        color: white;
    }

    .activity-icon.success {
        background-color: #28a745;
    }

    .activity-icon.warning {
        background-color: #ffc107;
    }

    .activity-icon.info {
        background-color: #17a2b8;
    }

    .activity-icon.danger {
        background-color: #dc3545;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h3 class="mb-2 text-success">Welcome back, <?= esc($user_name ?? 'User') ?>!</h3>
                            <p class="text-muted mb-0">Here's what's happening with your projects today. Role: <?= ucfirst(str_replace('_', ' ', $user_role ?? 'Officer')) ?></p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <div class="d-flex align-items-center justify-content-md-end">
                                <i class="fas fa-calendar-alt me-2 text-success"></i>
                                <span id="currentDate" class="text-light"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

        <!-- Statistics Cards -->
        <div class="row g-4 mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <i class="fas fa-clipboard-list fs-1 mb-3"></i>
                    <h3 class="fw-bold">24</h3>
                    <p class="mb-0">Active Workplans</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card green">
                    <i class="fas fa-coins fs-1 mb-3"></i>
                    <h3 class="fw-bold">K 12,450</h3>
                    <p class="mb-0">Pending Claims</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card blue">
                    <i class="fas fa-users fs-1 mb-3"></i>
                    <h3 class="fw-bold">156</h3>
                    <p class="mb-0">Total Users</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card orange">
                    <i class="fas fa-chart-bar fs-1 mb-3"></i>
                    <h3 class="fw-bold">K 89,750</h3>
                    <p class="mb-0">Budget Utilized</p>
                </div>
            </div>
        </div>

        <!-- Main Dashboard Content -->
        <div class="row g-4">
            <!-- Role-Based Navigation -->
            <div class="col-lg-8">
                <div class="dashboard-card p-4">
                    <h4 class="fw-bold mb-4" style="color: #800000;">
                        <i class="fas fa-th-large me-2"></i>My Dashboard
                    </h4>
                    <div class="row g-3">
                        <!-- Standard Buttons (Available to all users) -->
                        <div class="col-lg-6 col-md-6">
                            <a href="#" class="module-btn">
                                <i class="fas fa-tasks"></i>
                                <h6 class="fw-bold">My Workplan Activities</h6>
                                <small class="text-muted">View and manage assigned activities</small>
                            </a>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <a href="#" class="module-btn">
                                <i class="fas fa-user-circle"></i>
                                <h6 class="fw-bold">My Profile</h6>
                                <small class="text-muted">View and edit personal details</small>
                            </a>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <a href="#" class="module-btn">
                                <i class="fas fa-receipt"></i>
                                <h6 class="fw-bold">My Acquittals</h6>
                                <small class="text-muted">Submit and view acquittals</small>
                            </a>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <a href="#" class="module-btn">
                                <i class="fas fa-file-alt"></i>
                                <h6 class="fw-bold">My Reports</h6>
                                <small class="text-muted">Create and view reports</small>
                            </a>
                        </div>

                        <!-- Role-Specific Buttons (conditionally displayed) -->
                        <!-- Group Admin Button -->
                        <div class="col-lg-6 col-md-6" id="groupAdminBtn" style="display: none;">
                            <a href="#" class="module-btn">
                                <i class="fas fa-cogs"></i>
                                <h6 class="fw-bold">Group Settings</h6>
                                <small class="text-muted">Manage group details and activities</small>
                            </a>
                        </div>

                        <!-- Supervisor Button -->
                        <div class="col-lg-6 col-md-6" id="supervisorBtn" style="display: none;">
                            <a href="#" class="module-btn">
                                <i class="fas fa-project-diagram"></i>
                                <h6 class="fw-bold">Manage Workplans</h6>
                                <small class="text-muted">Create, assign and monitor workplans</small>
                            </a>
                        </div>

                        <!-- ARO/Fund Manager Financial Claims Button -->
                        <div class="col-lg-6 col-md-6" id="financialClaimsBtn" style="display: none;">
                            <a href="#" class="module-btn">
                                <i class="fas fa-coins"></i>
                                <h6 class="fw-bold">Financial Claims</h6>
                                <small class="text-muted">Create and manage financial claims</small>
                            </a>
                        </div>

                        <!-- Fund Manager Claims Workflow Button -->
                        <div class="col-lg-6 col-md-6" id="claimsWorkflowBtn" style="display: none;">
                            <a href="#" class="module-btn">
                                <i class="fas fa-clipboard-check"></i>
                                <h6 class="fw-bold">Claims Workflow</h6>
                                <small class="text-muted">Approve or reject pending claims</small>
                            </a>
                        </div>

                        <!-- Administrator Settings Button -->
                        <div class="col-lg-6 col-md-6" id="adminSettingsBtn" style="display: none;">
                            <a href="#" class="module-btn">
                                <i class="fas fa-tools"></i>
                                <h6 class="fw-bold">Administrator Settings</h6>
                                <small class="text-muted">User, structure, plans and budget management</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="col-lg-4">
                <div class="dashboard-card p-4">
                    <h4 class="fw-bold mb-4" style="color: #800000;">
                        <i class="fas fa-clock me-2"></i>Recent Activity
                    </h4>
                    <div class="recent-activity">
                        <div class="activity-item d-flex align-items-start">
                            <div class="activity-icon success me-3">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">Workplan Approved</h6>
                                <p class="mb-1 text-muted small">Q1 Marketing Plan has been approved by supervisor</p>
                                <small class="text-muted">2 hours ago</small>
                            </div>
                        </div>

                        <div class="activity-item d-flex align-items-start">
                            <div class="activity-icon info me-3">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">New User Added</h6>
                                <p class="mb-1 text-muted small">John Doe has been added to the system</p>
                                <small class="text-muted">4 hours ago</small>
                            </div>
                        </div>

                        <div class="activity-item d-flex align-items-start">
                            <div class="activity-icon warning me-3">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">Budget Alert</h6>
                                <p class="mb-1 text-muted small">IT Department budget K 85,000 (85%) utilized</p>
                                <small class="text-muted">6 hours ago</small>
                            </div>
                        </div>

                        <div class="activity-item d-flex align-items-start">
                            <div class="activity-icon success me-3">
                                <i class="fas fa-file-pdf"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">Report Generated</h6>
                                <p class="mb-1 text-muted small">Monthly progress report has been generated</p>
                                <small class="text-muted">1 day ago</small>
                            </div>
                        </div>

                        <div class="activity-item d-flex align-items-start">
                            <div class="activity-icon danger me-3">
                                <i class="fas fa-times"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">Claim Rejected</h6>
                                <p class="mb-1 text-muted small">Travel claim #TC-001 (K 2,500) requires additional documentation</p>
                                <small class="text-muted">2 days ago</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row g-4 mt-2">
            <div class="col-12">
                <div class="dashboard-card p-4">
                    <h4 class="fw-bold mb-4" style="color: #800000;">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h4>
                    <div class="row g-3">
                        <div class="col-lg-3 col-md-6">
                            <button class="btn btn-outline-primary w-100 py-3">
                                <i class="fas fa-plus-circle me-2"></i>Create Activity
                            </button>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <button class="btn btn-outline-success w-100 py-3">
                                <i class="fas fa-coins me-2"></i>Submit Claim
                            </button>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <button class="btn btn-outline-info w-100 py-3">
                                <i class="fas fa-chart-line me-2"></i>View Progress
                            </button>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <button class="btn btn-outline-warning w-100 py-3">
                                <i class="fas fa-file-upload me-2"></i>Submit Report
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Role Testing Section (for demo purposes) -->
        <div class="row g-4 mt-2">
            <div class="col-12">
                <div class="dashboard-card p-4">
                    <h4 class="fw-bold mb-4" style="color: #800000;">
                        <i class="fas fa-user-cog me-2"></i>Test Different User Roles
                    </h4>
                    <p class="text-muted mb-3">Click the buttons below to test the dashboard with different user roles:</p>
                    <div class="row g-2">
                        <div class="col-lg-2 col-md-4 col-sm-6">
                            <a href="<?= site_url('dashboard?role=officer') ?>" class="btn btn-outline-secondary w-100 btn-sm">
                                <i class="fas fa-user me-1"></i>Officer
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-sm-6">
                            <a href="<?= site_url('dashboard?role=group_admin') ?>" class="btn btn-outline-primary w-100 btn-sm">
                                <i class="fas fa-users-cog me-1"></i>Group Admin
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-sm-6">
                            <a href="<?= site_url('dashboard?role=supervisor') ?>" class="btn btn-outline-info w-100 btn-sm">
                                <i class="fas fa-user-tie me-1"></i>Supervisor
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-sm-6">
                            <a href="<?= site_url('dashboard?role=aro') ?>" class="btn btn-outline-warning w-100 btn-sm">
                                <i class="fas fa-file-invoice me-1"></i>ARO
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-sm-6">
                            <a href="<?= site_url('dashboard?role=fund_manager') ?>" class="btn btn-outline-success w-100 btn-sm">
                                <i class="fas fa-coins me-1"></i>Fund Manager
                            </a>
                        </div>
                        <div class="col-lg-2 col-md-4 col-sm-6">
                            <a href="<?= site_url('dashboard?role=administrator') ?>" class="btn btn-outline-danger w-100 btn-sm">
                                <i class="fas fa-user-shield me-1"></i>Administrator
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set current date
        const currentDate = new Date();
        const options = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        document.getElementById('currentDate').textContent = currentDate.toLocaleDateString('en-US', options);

        // Role-based button display logic
        // Get user role from server-side data
        const userRole = '<?= esc($user_role ?? 'officer') ?>';

        displayRoleBasedButtons(userRole);

        // Add click handlers for module buttons
        const moduleButtons = document.querySelectorAll('.module-btn');
        moduleButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const moduleName = this.querySelector('h6').textContent;
                alert(`${moduleName} module is not yet implemented in this demo.`);
            });
        });

        // Add click handlers for quick action buttons
        const quickActionButtons = document.querySelectorAll('.btn-outline-primary, .btn-outline-success, .btn-outline-info, .btn-outline-warning');
        quickActionButtons.forEach(button => {
            button.addEventListener('click', function() {
                const actionName = this.textContent.trim();
                alert(`${actionName} functionality is not yet implemented in this demo.`);
            });
        });

        // Animate statistics on page load
        const statCards = document.querySelectorAll('.stat-card');
        statCards.forEach((card, index) => {
            setTimeout(() => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'all 0.6s ease';

                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 100);
            }, index * 200);
        });

        // Add hover effects to dashboard cards
        const dashboardCards = document.querySelectorAll('.dashboard-card');
        dashboardCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });

        console.log('IPMS Dashboard initialized successfully');
    });

    // Function to get user role (now using server-side data)
    function getUserRole() {
        // This function is kept for compatibility but now uses server-side data
        return '<?= esc($user_role ?? 'officer') ?>';
    }

    // Function to display role-based buttons
    function displayRoleBasedButtons(userRole) {
        // Hide all role-specific buttons first
        document.getElementById('groupAdminBtn').style.display = 'none';
        document.getElementById('supervisorBtn').style.display = 'none';
        document.getElementById('financialClaimsBtn').style.display = 'none';
        document.getElementById('claimsWorkflowBtn').style.display = 'none';
        document.getElementById('adminSettingsBtn').style.display = 'none';

        // Show buttons based on user role
        switch(userRole) {
            case 'group_admin':
                document.getElementById('groupAdminBtn').style.display = 'block';
                break;
            case 'supervisor':
                document.getElementById('supervisorBtn').style.display = 'block';
                break;
            case 'aro':
                document.getElementById('financialClaimsBtn').style.display = 'block';
                break;
            case 'fund_manager':
                document.getElementById('financialClaimsBtn').style.display = 'block';
                document.getElementById('claimsWorkflowBtn').style.display = 'block';
                break;
            case 'administrator':
                document.getElementById('adminSettingsBtn').style.display = 'block';
                break;
            case 'officer':
            default:
                // Officer role only sees standard buttons (already visible)
                break;
        }

        console.log('Role-based buttons displayed for:', userRole);
    }
</script>
<?= $this->endSection() ?>