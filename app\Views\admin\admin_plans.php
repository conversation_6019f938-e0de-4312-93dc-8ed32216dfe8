<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('page_title') ?><?= esc($page_title) ?><?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
    .admin-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .admin-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.12);
    }

    .breadcrumb-item a {
        color: #800000;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #28a745;
    }

    .plan-card {
        border-left: 4px solid #28a745;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .plan-card:hover {
        border-left-color: #800000;
        transform: translateY(-3px);
    }

    .plan-card.corporate {
        border-left-color: #007bff;
    }

    .plan-card.development {
        border-left-color: #28a745;
    }

    .plan-type-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .nav-tabs .nav-link {
        color: #800000;
        border: none;
        border-bottom: 2px solid transparent;
    }

    .nav-tabs .nav-link.active {
        color: #28a745;
        border-bottom-color: #28a745;
        background: none;
    }

    .metric-card {
        text-align: center;
        padding: 1.5rem;
        border-radius: 10px;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    }

    .metric-value {
        font-size: 2rem;
        font-weight: bold;
        color: #800000;
    }

    .metric-label {
        color: #6c757d;
        font-size: 0.875rem;
        margin-top: 0.5rem;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="fade-in">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="mb-2 text-success">
                            <i class="fas fa-clipboard-list me-2"></i>Plans Management
                        </h3>
                        <p class="text-muted mb-0">
                            Manage corporate and development plans. Create KRAs, KPIs, programs, and projects linked to organizational goals.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="btn-group">
                            <a href="<?= site_url('admin') ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Admin
                            </a>
                            <button class="btn btn-success dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-plus me-2"></i>Create Plan
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#"><i class="fas fa-building me-2"></i>Corporate Plan</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-rocket me-2"></i>Development Plan</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Plans Overview -->
    <div class="row g-4 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="metric-card">
                <div class="metric-value"><?= count($corporate_plans) ?></div>
                <div class="metric-label">Corporate Plans</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="metric-card">
                <div class="metric-value"><?= count($development_plans) ?></div>
                <div class="metric-label">Development Plans</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="metric-card">
                <div class="metric-value">
                    <?= array_sum(array_column($corporate_plans, 'kras_count')) ?>
                </div>
                <div class="metric-label">Total KRAs</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="metric-card">
                <div class="metric-value">
                    <?= array_sum(array_column($development_plans, 'projects_count')) ?>
                </div>
                <div class="metric-label">Total Projects</div>
            </div>
        </div>
    </div>

    <!-- Plans Management Tabs -->
    <div class="row">
        <div class="col-12">
            <div class="admin-card p-4">
                <!-- Tab Navigation -->
                <ul class="nav nav-tabs mb-4" id="plansTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="corporate-tab" data-bs-toggle="tab"
                                data-bs-target="#corporate" type="button" role="tab">
                            <i class="fas fa-building me-2"></i>Corporate Plans
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="development-tab" data-bs-toggle="tab"
                                data-bs-target="#development" type="button" role="tab">
                            <i class="fas fa-rocket me-2"></i>Development Plans
                        </button>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content" id="plansTabContent">
                    <!-- Corporate Plans Tab -->
                    <div class="tab-pane fade show active" id="corporate" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="fw-bold mb-0" style="color: #800000;">
                                <i class="fas fa-building me-2"></i>Corporate Plans
                            </h5>
                            <button class="btn btn-primary btn-sm">
                                <i class="fas fa-plus me-1"></i>Add Corporate Plan
                            </button>
                        </div>

                        <div class="row g-4">
                            <?php foreach ($corporate_plans as $plan): ?>
                            <div class="col-lg-6">
                                <div class="plan-card corporate admin-card p-4">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div>
                                            <h6 class="fw-bold mb-1"><?= esc($plan['name']) ?></h6>
                                            <span class="badge bg-primary plan-type-badge"><?= esc($plan['type']) ?></span>
                                        </div>
                                        <div class="dropdown">
                                            <button class="btn btn-outline-secondary btn-sm" type="button" data-bs-toggle="dropdown">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#"><i class="fas fa-eye me-2"></i>View Details</a></li>
                                                <li><a class="dropdown-item" href="#"><i class="fas fa-edit me-2"></i>Edit Plan</a></li>
                                                <li><a class="dropdown-item" href="#"><i class="fas fa-plus me-2"></i>Add KRA</a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#"><i class="fas fa-trash me-2"></i>Delete</a></li>
                                            </ul>
                                        </div>
                                    </div>

                                    <div class="row text-center">
                                        <div class="col-4">
                                            <div class="fw-bold text-primary"><?= $plan['fiscal_year'] ?></div>
                                            <small class="text-muted">Fiscal Year</small>
                                        </div>
                                        <div class="col-4">
                                            <div class="fw-bold text-success"><?= $plan['kras_count'] ?></div>
                                            <small class="text-muted">KRAs</small>
                                        </div>
                                        <div class="col-4">
                                            <div class="fw-bold text-info"><?= $plan['kpis_count'] ?></div>
                                            <small class="text-muted">KPIs</small>
                                        </div>
                                    </div>

                                    <div class="mt-3">
                                        <div class="btn-group w-100" role="group">
                                            <button class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-tasks me-1"></i>Manage KRAs
                                            </button>
                                            <button class="btn btn-outline-success btn-sm">
                                                <i class="fas fa-chart-line me-1"></i>Manage KPIs
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Development Plans Tab -->
                    <div class="tab-pane fade" id="development" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="fw-bold mb-0" style="color: #800000;">
                                <i class="fas fa-rocket me-2"></i>Development Plans
                            </h5>
                            <button class="btn btn-success btn-sm">
                                <i class="fas fa-plus me-1"></i>Add Development Plan
                            </button>
                        </div>

                        <div class="row g-4">
                            <?php foreach ($development_plans as $plan): ?>
                            <div class="col-lg-6">
                                <div class="plan-card development admin-card p-4">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div>
                                            <h6 class="fw-bold mb-1"><?= esc($plan['name']) ?></h6>
                                            <span class="badge bg-success plan-type-badge"><?= esc($plan['type']) ?></span>
                                        </div>
                                        <div class="dropdown">
                                            <button class="btn btn-outline-secondary btn-sm" type="button" data-bs-toggle="dropdown">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#"><i class="fas fa-eye me-2"></i>View Details</a></li>
                                                <li><a class="dropdown-item" href="#"><i class="fas fa-edit me-2"></i>Edit Plan</a></li>
                                                <li><a class="dropdown-item" href="#"><i class="fas fa-plus me-2"></i>Add Program</a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#"><i class="fas fa-trash me-2"></i>Delete</a></li>
                                            </ul>
                                        </div>
                                    </div>

                                    <div class="row text-center">
                                        <div class="col-4">
                                            <div class="fw-bold text-primary"><?= $plan['fiscal_year'] ?></div>
                                            <small class="text-muted">Fiscal Year</small>
                                        </div>
                                        <div class="col-4">
                                            <div class="fw-bold text-warning"><?= $plan['programs_count'] ?></div>
                                            <small class="text-muted">Programs</small>
                                        </div>
                                        <div class="col-4">
                                            <div class="fw-bold text-info"><?= $plan['projects_count'] ?></div>
                                            <small class="text-muted">Projects</small>
                                        </div>
                                    </div>

                                    <div class="mt-3">
                                        <div class="btn-group w-100" role="group">
                                            <button class="btn btn-outline-warning btn-sm">
                                                <i class="fas fa-layer-group me-1"></i>Manage Programs
                                            </button>
                                            <button class="btn btn-outline-info btn-sm">
                                                <i class="fas fa-project-diagram me-1"></i>Manage Projects
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="admin-card p-4">
                <h5 class="fw-bold mb-3" style="color: #800000;">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h5>
                <div class="row g-3">
                    <div class="col-lg-3 col-md-6">
                        <button class="btn btn-outline-primary w-100 py-3">
                            <i class="fas fa-plus-circle me-2"></i>Create Corporate Plan
                        </button>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <button class="btn btn-outline-success w-100 py-3">
                            <i class="fas fa-rocket me-2"></i>Create Development Plan
                        </button>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <button class="btn btn-outline-info w-100 py-3">
                            <i class="fas fa-chart-line me-2"></i>View Reports
                        </button>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <button class="btn btn-outline-warning w-100 py-3">
                            <i class="fas fa-download me-2"></i>Export Plans
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="d-flex gap-2">
                <a href="<?= site_url('admin') ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Admin Settings
                </a>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Plan card click handlers
        document.querySelectorAll('.plan-card').forEach(card => {
            card.addEventListener('click', function(e) {
                // Don't trigger if clicking on dropdown or buttons
                if (e.target.closest('.dropdown') || e.target.closest('.btn-group')) {
                    return;
                }

                // Add visual feedback
                this.style.transform = 'translateY(-5px)';
                setTimeout(() => {
                    this.style.transform = 'translateY(-3px)';
                }, 150);

                // In a real implementation, this would navigate to plan details
                console.log('Plan card clicked');
            });
        });

        console.log('Plans Management initialized successfully');
    });
</script>
<?= $this->endSection() ?>
